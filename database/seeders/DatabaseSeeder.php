<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        $this->call([
            QuranSeeder::class,
            GuardianSeeder::class,
            TeacherSeeder::class,
            ClassroomSeeder::class,
            StudentSeeder::class,
            ClassroomScheduleSeeder::class,
            SettingSeeder::class,
            ShieldSeeder::class,
            AttendanceSeeder::class,
        ]);

        // Create test user if it doesn't exist
        if (! User::where('email', '<EMAIL>')->exists()) {
            $user = User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ]);

            // select all permissions
            $permissions = \Spatie\Permission\Models\Permission::all();

            // add all permissions to the role "super-admin"
            $role = \Spatie\Permission\Models\Role::findOrCreate('super-admin', 'web');
            $role->syncPermissions($permissions);

            $user->assignRole('super-admin');
        }
    }
}
