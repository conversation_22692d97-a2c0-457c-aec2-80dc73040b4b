<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AttendanceScheduleResource\Pages\CreateAttendanceSchedule;
use App\Filament\Resources\AttendanceScheduleResource\Pages\EditAttendanceSchedule;
use App\Filament\Resources\AttendanceScheduleResource\Pages\ListAttendanceSchedules;
use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User;
use BackedEnum;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use UnitEnum;

class AttendanceScheduleResource extends Resource
{
    protected static ?string $model = AttendanceSchedule::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-calendar-days';

    protected static string|UnitEnum|null $navigationGroup = 'Attendance';

    protected static ?int $navigationSort = 3;

    public static function getPluralModelLabel(): string
    {
        return __('Attendance Records');
    }

    public static function getLabel(): string
    {
        return __('Attendance Record');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('student_id')
                    ->label('Student')
                    ->translateLabel()
                    ->options(Student::query()->pluck('name', 'id'))
                    ->searchable()
                    ->required(),
                Select::make('classroom_id')
                    ->label('Classroom')
                    ->translateLabel()
                    ->options(Classroom::query()->pluck('name', 'id'))
                    ->required(),
                Select::make('type')
                    ->label('Attendance Type')
                    ->translateLabel()
                    ->options([
                        'Present' => 'Present',
                        'Absent' => 'Absent',
                    ])
                    ->default('Absent')
                    ->required(),
                DatePicker::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->default(now())
                    ->required(),
                Select::make('operator_type')
                    ->label('Recorded By Type')
                    ->translateLabel()
                    ->options([
                        'App\Models\Teacher' => 'Teacher',
                        'App\Models\User' => 'User',
                    ])
                    ->reactive()
                    ->required(),
                Select::make('operator_id')
                    ->label('Recorded By')
                    ->translateLabel()
                    ->options(function (callable $get) {
                        $operatorType = $get('operator_type');
                        if ($operatorType === 'App\Models\Teacher') {
                            return Teacher::query()->pluck('name', 'id');
                        }
                        if ($operatorType === 'App\Models\User') {
                            return User::query()->pluck('name', 'id');
                        }

                        return [];
                    })
                    ->required(),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('student.name')
                    ->label('Student')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                BadgeColumn::make('type')
                    ->label('Status')
                    ->translateLabel()
                    ->colors([
                        'success' => 'Present',
                        'danger' => 'Absent',
                    ])
                    ->sortable(),
                TextColumn::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->date()
                    ->sortable(),
                TextColumn::make('operator.name')
                    ->label('Recorded By')
                    ->translateLabel()
                    ->sortable(),
                TextColumn::make('operator_type')
                    ->label('Operator Type')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'App\Models\Teacher' => 'Teacher',
                        'App\Models\User' => 'User',
                        default => $state,
                    })
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'Present' => 'Present',
                        'Absent' => 'Absent',
                    ]),
                SelectFilter::make('classroom_id')
                    ->label('Classroom')
                    ->translateLabel()
                    ->options(Classroom::query()->pluck('name', 'id')),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAttendanceSchedules::route('/'),
            'create' => CreateAttendanceSchedule::route('/create'),
            'edit' => EditAttendanceSchedule::route('/{record}/edit'),
        ];
    }
}
